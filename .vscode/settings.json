{
  "eslint.workingDirectories": [
    {
      "mode": "auto"
    }
  ],
  "css.customData": [
    ".vscode/tailwind.json"
  ],
  "files.associations": {
    "*.css": "tailwindcss",
    "*.vue": "vue"
  },
  "tailwindCSS.experimental.classRegex": [
    [
      "ui:\\s*{([^)]*)\\s*}",
      "[\"'`]([^\"'`]*).*?[\"'`]"
    ],
    [
      "/\\*\\s?ui\\s?\\*/\\s*{([^;]*)}",
      ":\\s*[\"'`]([^\"'`]*).*?[\"'`]"
    ]
  ],
  "tailwindCSS.classAttributes": [
    "class",
    "className",
    "ngClass",
    "ui"
  ],
  "tailwindCSS.experimental.configFile": "tailwind.config.ts",
  "files.exclude": {
    "node_modules": true,
    "build": true,
    "**/.output": true,
    "**/.open-next": true,
    "**/.wrangler": true,
    "**/.turbo": true,
    // "**/dist": true,
    "**/node_modules": true,
    "**/build": true,
    "**/.next": true
  }
}