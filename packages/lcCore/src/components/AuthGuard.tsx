"use client";
import { useEffect, type ReactNode } from "react";
import { useAuth } from "../providers/AuthProvider";

type AuthGuardProps = {
  children: ReactNode;
  redirectTo?: string;
  fallback?: ReactNode;
  onRedirect?: (redirectTo: string) => void;
};

/**
 * AuthGuard component that protects routes by checking authentication status
 * Calls onRedirect callback for unauthenticated users (allows custom redirect logic)
 */
const AuthGuard = ({
  children,
  redirectTo = "/login",
  fallback = <div>Loading...</div>,
  onRedirect
}: AuthGuardProps) => {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    // Only redirect if we're done loading and user is not authenticated
    if (!isLoading && !isAuthenticated && onRedirect) {
      onRedirect(redirectTo);
    }
  }, [isAuthenticated, isLoading, onRedirect, redirectTo]);

  // Show loading state while checking authentication
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Show loading state while redirecting
  if (!isAuthenticated) {
    return <>{fallback}</>;
  }

  // User is authenticated, render children
  return <>{children}</>;
};

export default AuthGuard;
