"use client";
import type { ReactNode } from "react";
import { createContext, useContext, useEffect, useState } from "react";
import { lcFbAuth, lcFbOnAuthStateChanged, lcFbSignOut } from "@/firebase";
import type { User } from "firebase/auth";

type AuthContextType = {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: () => void;
  logout: () => Promise<void>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);

  // Manual login function (for programmatic login)
  const login = () => setIsAuthenticated(true);

  // Logout function that signs out from Firebase
  const logout = async () => {
    try {
      await lcFbSignOut(lcFbAuth);
      // Firebase auth state change will handle setting isAuthenticated to false
    } catch (error) {
      console.error("Error signing out:", error);
      // Force logout even if Firebase signout fails
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  useEffect(() => {
    const unsubscribe = lcFbOnAuthStateChanged(
      lcFbAuth,
      (user: User | null) => {
        setUser(user);
        // Connect Firebase user state to isAuthenticated
        setIsAuthenticated(!!user);
        setIsLoading(false);
      }
    );
    return () => unsubscribe();
  }, []);

  return (
    <AuthContext.Provider value={{ isAuthenticated, isLoading, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

export default AuthProvider;
