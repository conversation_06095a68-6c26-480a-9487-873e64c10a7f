{"name": "@gd/core", "version": "0.0.1", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "module": "./dist/index.js", "types": "./dist/index.d.ts", "files": ["dist"], "scripts": {"build:core": "rslib build && node ./script/encode.mjs", "check": "biome check --write", "dev": "rslib build --watch", "format": "biome format --write"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@rsbuild/plugin-react": "^1.3.2", "@rslib/core": "^0.9.2", "@types/react": "^19.1.6", "javascript-obfuscator": "^4.1.1", "react": "^19.1.0", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">=19.1.0", "react-dom": ">=19.1.0"}, "private": true, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@types/node": "^22.15.30", "firebase": "^11.9.0", "ofetch": "^1.4.1", "react-redux": "^9.2.0", "zod": "^3.25.56"}}