import type { FirestoreValue, DocumentData } from "./types";

/**
 * Converts a JavaScript value to Firestore value format
 */
export const toFirestoreValue = (value: unknown): FirestoreValue => {
  if (value === null || value === undefined) {
    return { nullValue: null };
  }
  
  if (typeof value === "string") {
    return { stringValue: value };
  }
  
  if (typeof value === "number") {
    return Number.isInteger(value) 
      ? { integerValue: value.toString() }
      : { doubleValue: value };
  }
  
  if (typeof value === "boolean") {
    return { booleanValue: value };
  }
  
  if (value instanceof Date) {
    return { timestampValue: value.toISOString() };
  }
  
  if (Array.isArray(value)) {
    return {
      arrayValue: {
        values: value.map(toFirestoreValue)
      }
    };
  }
  
  if (typeof value === "object") {
    const fields: Record<string, FirestoreValue> = {};
    for (const [key, val] of Object.entries(value)) {
      fields[key] = toFirestoreValue(val);
    }
    return { mapValue: { fields } };
  }
  
  throw new Error(`Unsupported value type: ${typeof value}`);
};

/**
 * Converts Firestore value format to JavaScript value
 */
export const fromFirestoreValue = (value: FirestoreValue): unknown => {
  if ("nullValue" in value) {
    return null;
  }
  
  if ("stringValue" in value) {
    return value.stringValue;
  }
  
  if ("integerValue" in value) {
    return parseInt(value.integerValue, 10);
  }
  
  if ("doubleValue" in value) {
    return value.doubleValue;
  }
  
  if ("booleanValue" in value) {
    return value.booleanValue;
  }
  
  if ("timestampValue" in value) {
    return new Date(value.timestampValue);
  }
  
  if ("arrayValue" in value) {
    return value.arrayValue.values.map(fromFirestoreValue);
  }
  
  if ("mapValue" in value) {
    const result: Record<string, any> = {};
    for (const [key, val] of Object.entries(value.mapValue.fields)) {
      result[key] = fromFirestoreValue(val);
    }
    return result;
  }
  
  if ("referenceValue" in value) {
    return value.referenceValue;
  }
  
  throw new Error(`Unsupported Firestore value: ${JSON.stringify(value)}`);
};

/**
 * Converts a document data object to Firestore fields format
 */
export const toFirestoreFields = (data: DocumentData): Record<string, FirestoreValue> => {
  const fields: Record<string, FirestoreValue> = {};
  for (const [key, value] of Object.entries(data)) {
    fields[key] = toFirestoreValue(value);
  }
  return fields;
};

/**
 * Converts Firestore fields format to document data object
 */
export const fromFirestoreFields = (fields: Record<string, FirestoreValue>): DocumentData => {
  const data: DocumentData = {};
  for (const [key, value] of Object.entries(fields)) {
    data[key] = fromFirestoreValue(value);
  }
  return data;
};

/**
 * Extracts document ID from Firestore document name
 */
export const extractDocumentId = (documentName: string): string => {
  const parts = documentName.split("/");
  const lastPart = parts[parts.length - 1];
  return lastPart || "";
};

/**
 * Builds Firestore document path
 */
export const buildDocumentPath = (projectId: string, collection: string, documentId?: string): string => {
  const basePath = `projects/${projectId}/databases/(default)/documents/${collection}`;
  return documentId ? `${basePath}/${documentId}` : basePath;
};

/**
 * Validates collection name
 */
export const validateCollectionName = (collection: string): void => {
  if (!collection || typeof collection !== "string") {
    throw new Error("Collection name must be a non-empty string");
  }
  
  if (collection.includes("/")) {
    throw new Error("Collection name cannot contain forward slashes");
  }
  
  if (collection.startsWith("__")) {
    throw new Error("Collection name cannot start with double underscores");
  }
};

/**
 * Validates document ID
 */
export const validateDocumentId = (documentId: string): void => {
  if (!documentId || typeof documentId !== "string") {
    throw new Error("Document ID must be a non-empty string");
  }
  
  if (documentId.includes("/")) {
    throw new Error("Document ID cannot contain forward slashes");
  }
  
  if (documentId === "." || documentId === "..") {
    throw new Error("Document ID cannot be '.' or '..'");
  }
  
  if (documentId.length > 1500) {
    throw new Error("Document ID cannot exceed 1500 characters");
  }
};

/**
 * Generates a random document ID
 */
export const generateDocumentId = (): string => {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < 20; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

/**
 * Handles Firestore API errors
 */
export const handleFirestoreError = (error: unknown): string => {
  if (error && typeof error === 'object' && 'error' in error) {
    const errorObj = error as { error?: { message?: string } };
    if (errorObj.error?.message) {
      return errorObj.error.message;
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return "An unknown error occurred while accessing Firestore";
};
