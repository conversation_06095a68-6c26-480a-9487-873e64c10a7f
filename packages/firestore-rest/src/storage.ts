/**
 * Internal storage abstraction for Firebase tokens
 * Automatically handles browser vs server environments with cookie-based token sharing
 */

/**
 * Environment detection utilities (internal)
 */
const isBrowser = (): boolean => {
  return typeof window !== "undefined" && typeof window.document !== "undefined";
};

const isServer = (): boolean => {
  return !isBrowser();
};

/**
 * Cookie utilities for server-client token sharing
 */
class CookieManager {
  private static readonly COOKIE_OPTIONS = {
    httpOnly: false, // Allow client-side access
    secure: false, // Set to true in production with HTTPS
    sameSite: 'lax' as const,
    maxAge: 60 * 60 * 24 * 7, // 7 days
    path: '/',
  };

  /**
   * Set cookie in browser environment
   */
  static setCookie(name: string, value: string, options: Partial<typeof CookieManager.COOKIE_OPTIONS> = {}): void {
    if (!isBrowser()) return;

    const opts = { ...CookieManager.COOKIE_OPTIONS, ...options };
    const cookieString = [
      `${name}=${encodeURIComponent(value)}`,
      `path=${opts.path}`,
      `max-age=${opts.maxAge}`,
      `samesite=${opts.sameSite}`,
      opts.secure ? 'secure' : '',
      opts.httpOnly ? 'httponly' : '',
    ].filter(Boolean).join('; ');

    document.cookie = cookieString;
  }

  /**
   * Get cookie value (works in both browser and server with headers)
   */
  static getCookie(name: string, cookieHeader?: string): string | null {
    let cookieString: string;

    if (isBrowser()) {
      cookieString = document.cookie;
    } else if (cookieHeader) {
      cookieString = cookieHeader;
    } else {
      // In server environment without explicit cookie header
      // Cannot access cookies directly - return null
      // Consumers should pass cookieHeader explicitly in server environments
      return null;
    }

    const cookies = cookieString.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      if (key && value) {
        acc[key] = decodeURIComponent(value);
      }
      return acc;
    }, {} as Record<string, string>);

    return cookies[name] || null;
  }

  /**
   * Remove cookie
   */
  static removeCookie(name: string): void {
    if (!isBrowser()) return;
    
    document.cookie = `${name}=; path=/; max-age=0`;
  }
}

/**
 * Internal storage interface for Firebase tokens
 * Automatically handles localStorage (browser) and cookies (server/client sharing)
 */
class InternalStorage {
  private static readonly TOKEN_KEYS = {
    CUSTOM_TOKEN: 'firebase_custom_token',
    ID_TOKEN: 'firebase_id_token',
    REFRESH_TOKEN: 'firebase_refresh_token',
    EXPIRY: 'firebase_token_expiry',
  };

  private memoryStorage = new Map<string, string>();

  /**
   * Store token with automatic environment handling
   */
  setToken(key: string, value: string): void {
    if (isBrowser()) {
      // In browser: store in localStorage and cookie for server access
      try {
        localStorage.setItem(key, value);
      } catch {
        // Fallback to memory if localStorage is not available
        this.memoryStorage.set(key, value);
      }
      
      // Also store in cookie for server-side access
      CookieManager.setCookie(key, value);
    } else {
      // In server: store in memory only (cookies are read-only)
      this.memoryStorage.set(key, value);
    }
  }

  /**
   * Get token with automatic environment handling
   */
  getToken(key: string, cookieHeader?: string): string | null {
    if (isBrowser()) {
      // In browser: try localStorage first, then cookies
      try {
        const value = localStorage.getItem(key);
        if (value) return value;
      } catch {
        // Continue to cookie fallback
      }
      
      return CookieManager.getCookie(key);
    } else {
      // In server: try memory first, then cookies
      const memoryValue = this.memoryStorage.get(key);
      if (memoryValue) return memoryValue;
      
      return CookieManager.getCookie(key, cookieHeader);
    }
  }

  /**
   * Remove token from all storage locations
   */
  removeToken(key: string): void {
    if (isBrowser()) {
      try {
        localStorage.removeItem(key);
      } catch {
        // Continue to cookie removal
      }
      CookieManager.removeCookie(key);
    } else {
      this.memoryStorage.delete(key);
    }
  }

  /**
   * Clear all tokens
   */
  clearAllTokens(): void {
    const keys = Object.values(InternalStorage.TOKEN_KEYS);
    keys.forEach(key => this.removeToken(key));
    
    if (isServer()) {
      this.memoryStorage.clear();
    }
  }

  /**
   * Store Firebase authentication tokens
   */
  storeAuthTokens(tokens: {
    idToken: string;
    refreshToken?: string;
    expiresIn?: string;
    customToken?: string;
  }): void {
    this.setToken(InternalStorage.TOKEN_KEYS.ID_TOKEN, tokens.idToken);
    
    if (tokens.refreshToken) {
      this.setToken(InternalStorage.TOKEN_KEYS.REFRESH_TOKEN, tokens.refreshToken);
    }
    
    if (tokens.expiresIn) {
      const expiryTime = Date.now() + (parseInt(tokens.expiresIn) * 1000);
      this.setToken(InternalStorage.TOKEN_KEYS.EXPIRY, expiryTime.toString());
    }
    
    if (tokens.customToken) {
      this.setToken(InternalStorage.TOKEN_KEYS.CUSTOM_TOKEN, tokens.customToken);
    }
  }

  /**
   * Get stored authentication tokens
   */
  getAuthTokens(cookieHeader?: string): {
    idToken: string | null;
    refreshToken: string | null;
    customToken: string | null;
    isExpired: boolean;
  } {
    const idToken = this.getToken(InternalStorage.TOKEN_KEYS.ID_TOKEN, cookieHeader);
    const refreshToken = this.getToken(InternalStorage.TOKEN_KEYS.REFRESH_TOKEN, cookieHeader);
    const customToken = this.getToken(InternalStorage.TOKEN_KEYS.CUSTOM_TOKEN, cookieHeader);
    const expiryTime = this.getToken(InternalStorage.TOKEN_KEYS.EXPIRY, cookieHeader);
    
    const isExpired = expiryTime ? Date.now() > parseInt(expiryTime) : true;
    
    return {
      idToken,
      refreshToken,
      customToken,
      isExpired,
    };
  }
}

/**
 * Singleton instance for internal use
 */
export const internalStorage = new InternalStorage();

/**
 * Export utilities for internal package use only
 */
export { isBrowser, isServer, CookieManager };
