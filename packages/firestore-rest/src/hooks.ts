import { useState, useEffect, useCallback } from "react";
import type {
  DocumentData,
  QueryOptions,
  Where<PERSON><PERSON><PERSON>,
  UseFirestoreResult,
  UseFirestoreListResult
} from "./types";
import { FirestoreOperations } from "./operations";
import type { FirebaseUserInfo } from "./auth";

/**
 * React hooks for Firestore operations with LC backend integration
 */

/**
 * Hook to get a single document
 */
export const useDocument = (
  firestore: FirestoreOperations,
  collection: string,
  documentId: string | null
): UseFirestoreResult<DocumentData> => {
  const [data, setData] = useState<DocumentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = useCallback(async () => {
    if (!documentId) {
      setData(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const result = await firestore.get(collection, documentId);
      setData(result?.data || null);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch document";
      setError(errorMessage);
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [firestore, collection, documentId]);

  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return {
    data,
    loading,
    error,
    refetch: fetchDocument,
  };
};

/**
 * Hook to get all documents in a collection
 */
export const useCollection = (
  firestore: FirestoreOperations,
  collection: string
): UseFirestoreListResult<DocumentData & { id: string }> => {
  const [data, setData] = useState<Array<DocumentData & { id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCollection = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const results = await firestore.getAll(collection);
      setData(results.map(result => ({ ...result.data, id: result.id })));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch collection";
      setError(errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [firestore, collection]);

  useEffect(() => {
    fetchCollection();
  }, [fetchCollection]);

  return {
    data,
    loading,
    error,
    hasMore: false, // Not applicable for getAll
    loadMore: async () => {}, // Not applicable for getAll
    refetch: fetchCollection,
  };
};

/**
 * Hook to query documents with filters
 */
export const useQuery = (
  firestore: FirestoreOperations,
  collection: string,
  options: QueryOptions
): UseFirestoreListResult<DocumentData & { id: string }> => {
  const [data, setData] = useState<Array<DocumentData & { id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchQuery = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const results = await firestore.query(collection, options);
      setData(results.map(result => ({ ...result.data, id: result.id })));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to execute query";
      setError(errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [firestore, collection, JSON.stringify(options)]);

  useEffect(() => {
    fetchQuery();
  }, [fetchQuery]);

  return {
    data,
    loading,
    error,
    hasMore: false, // Would need pagination support
    loadMore: async () => {}, // Would need pagination support
    refetch: fetchQuery,
  };
};

/**
 * Hook for paginated data
 */
export const usePagination = (
  firestore: FirestoreOperations,
  collection: string,
  pageSize: number = 10
): UseFirestoreListResult<DocumentData & { id: string }> => {
  const [data, setData] = useState<Array<DocumentData & { id: string }>>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nextPageToken, setNextPageToken] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(false);

  const fetchPage = useCallback(async (pageToken?: string, append: boolean = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await firestore.paginate(collection, pageSize, pageToken);
      const newData = result.documents.map(doc => ({ ...doc.data, id: doc.id }));
      
      if (append) {
        setData(prev => [...prev, ...newData]);
      } else {
        setData(newData);
      }
      
      setNextPageToken(result.nextPageToken);
      setHasMore(result.hasMore);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch page";
      setError(errorMessage);
      if (!append) {
        setData([]);
      }
    } finally {
      setLoading(false);
    }
  }, [firestore, collection, pageSize]);

  const loadMore = useCallback(async () => {
    if (nextPageToken && hasMore) {
      await fetchPage(nextPageToken, true);
    }
  }, [fetchPage, nextPageToken, hasMore]);

  const refetch = useCallback(async () => {
    setData([]);
    setNextPageToken(undefined);
    await fetchPage();
  }, [fetchPage]);

  useEffect(() => {
    fetchPage();
  }, [fetchPage]);

  return {
    data,
    loading,
    error,
    hasMore,
    loadMore,
    refetch,
  };
};

/**
 * Hook for real-time-like updates (polling-based)
 */
export const useRealtimeDocument = (
  firestore: FirestoreOperations,
  collection: string,
  documentId: string | null,
  pollingInterval: number = 5000
): UseFirestoreResult<DocumentData> => {
  const [data, setData] = useState<DocumentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDocument = useCallback(async () => {
    if (!documentId) {
      setData(null);
      setLoading(false);
      return;
    }

    try {
      if (loading) {
        setError(null);
      }
      
      const result = await firestore.get(collection, documentId);
      setData(result?.data || null);
      
      if (loading) {
        setLoading(false);
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to fetch document";
      setError(errorMessage);
      setData(null);
      if (loading) {
        setLoading(false);
      }
    }
  }, [firestore, collection, documentId, loading]);

  useEffect(() => {
    fetchDocument();
    
    // Set up polling
    const interval = setInterval(fetchDocument, pollingInterval);
    
    return () => clearInterval(interval);
  }, [fetchDocument, pollingInterval]);

  return {
    data,
    loading,
    error,
    refetch: fetchDocument,
  };
};

/**
 * Hook for search functionality
 */
export const useSearch = (
  firestore: FirestoreOperations,
  collection: string,
  field: string,
  searchTerm: string,
  debounceMs: number = 300
): UseFirestoreListResult<DocumentData & { id: string }> => {
  const [data, setData] = useState<Array<DocumentData & { id: string }>>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const performSearch = useCallback(async (term: string) => {
    if (!term.trim()) {
      setData([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const results = await firestore.search(collection, field, term);
      setData(results.map(result => ({ ...result.data, id: result.id })));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Search failed";
      setError(errorMessage);
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [firestore, collection, field]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timeoutId);
  }, [performSearch, searchTerm, debounceMs]);

  return {
    data,
    loading,
    error,
    hasMore: false,
    loadMore: async () => {},
    refetch: () => performSearch(searchTerm),
  };
};

/**
 * Hook for CRUD operations
 */
export const useFirestoreCRUD = (
  firestore: FirestoreOperations,
  collection: string
) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const create = useCallback(async (data: DocumentData, documentId?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = documentId 
        ? await firestore.set(collection, documentId, data)
        : await firestore.add(collection, data);
      
      return result;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to create document";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [firestore, collection]);

  const update = useCallback(async (documentId: string, data: DocumentData) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await firestore.update(collection, documentId, data);
      return result;
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to update document";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [firestore, collection]);

  const remove = useCallback(async (documentId: string) => {
    try {
      setLoading(true);
      setError(null);
      
      await firestore.delete(collection, documentId);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : "Failed to delete document";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [firestore, collection]);

  return {
    create,
    update,
    remove,
    loading,
    error,
  };
};

/**
 * Hook for authentication status
 */
export const useFirestoreAuth = (firestore: FirestoreOperations) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<FirebaseUserInfo | null>(null);
  const [loading, setLoading] = useState(true);

  const checkAuth = useCallback(async () => {
    try {
      setLoading(true);
      const client = firestore.getClient();
      const authenticated = await client.isAuthenticated();
      setIsAuthenticated(authenticated);
      
      if (authenticated) {
        const userInfo = await client.getCurrentUser();
        setUser(userInfo);
      } else {
        setUser(null);
      }
    } catch (error) {
      setIsAuthenticated(false);
      setUser(null);
    } finally {
      setLoading(false);
    }
  }, [firestore]);

  const signOut = useCallback(() => {
    const client = firestore.getClient();
    client.signOut();
    setIsAuthenticated(false);
    setUser(null);
  }, [firestore]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    isAuthenticated,
    user,
    loading,
    signOut,
    refetch: checkAuth,
  };
};
