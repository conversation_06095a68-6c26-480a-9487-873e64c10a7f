import { ofetch } from "ofetch";
import type { LCAuthToken, LCAuthConfig } from "./types";
import { internalStorage } from "./storage";

/**
 * Firebase Auth API Response Types
 */
export interface FirebaseAuthResponse {
  idToken: string;
  refreshToken: string;
  expiresIn: string;
  localId: string;
  email?: string;
}

export interface FirebaseRefreshResponse {
  access_token: string;
  expires_in: string;
  token_type: string;
  refresh_token: string;
  id_token: string;
  user_id: string;
  project_id: string;
}

export interface FirebaseUserInfo {
  localId: string;
  email?: string;
  emailVerified?: boolean;
  displayName?: string;
  photoUrl?: string;
  passwordHash?: string;
  passwordUpdatedAt?: number;
  validSince?: string;
  disabled?: boolean;
  lastLoginAt?: string;
  createdAt?: string;
  customAuth?: boolean;
}

export interface FirebaseUserLookupResponse {
  users: FirebaseUserInfo[];
}

/**
 * Enhanced authentication utilities for Firestore REST API
 * Integrates with LC backend authentication flow
 */

/**
 * LC Backend Authentication Manager
 * Handles custom tokens from the LC backend and Firebase Auth integration
 * Automatically manages token storage and synchronization between server/client
 */
export class LCFirebaseAuth {
  private config: LCAuthConfig;
  private currentToken: string | null = null;
  private tokenExpiry: number | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;
  private cookieHeader: string | undefined;

  constructor(config: LCAuthConfig, cookieHeader?: string) {
    this.config = config;
    this.cookieHeader = cookieHeader;

    // Automatically load existing tokens on initialization
    this.loadExistingTokens();
  }

  /**
   * Load existing tokens from storage (cookies/localStorage)
   */
  private loadExistingTokens(): void {
    const tokens = internalStorage.getAuthTokens(this.cookieHeader);

    if (tokens.idToken && !tokens.isExpired) {
      this.currentToken = tokens.idToken;
      // Calculate expiry from stored expiry time
      const expiryStr = internalStorage.getToken('firebase_token_expiry', this.cookieHeader);
      if (expiryStr) {
        this.tokenExpiry = parseInt(expiryStr);
      }
    }

    // If we have a custom token but no valid ID token, store it for later use
    if (tokens.customToken && !this.config.customToken) {
      this.config.customToken = tokens.customToken;
    }
  }

  /**
   * Set custom token from LC backend
   */
  public setCustomToken(token: string): void {
    this.config.customToken = token;
    this.currentToken = null; // Reset current token to force refresh

    // Store custom token for automatic detection
    internalStorage.storeAuthTokens({
      idToken: '', // Will be filled when exchanged
      customToken: token
    });
  }

  /**
   * Exchange custom token for Firebase ID token
   */
  public async exchangeCustomToken(customToken?: string): Promise<LCAuthToken> {
    const token = customToken || this.config.customToken;
    
    if (!token) {
      throw new Error("No custom token available for exchange");
    }

    const response = await ofetch<FirebaseAuthResponse>(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithCustomToken?key=${this.config.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          token,
          returnSecureToken: true,
        },
      }
    );

    const authResult: LCAuthToken = {
      idToken: response.idToken,
      refreshToken: response.refreshToken,
      expiresIn: response.expiresIn,
      localId: response.localId,
      ...(response.email && { email: response.email }),
    };

    // Store token and set up auto-refresh
    this.currentToken = authResult.idToken;
    this.tokenExpiry = Date.now() + (parseInt(authResult.expiresIn || "3600") * 1000);
    this.setupTokenRefresh(authResult.refreshToken);

    // Store tokens in internal storage for automatic synchronization
    const tokensToStore: {
      idToken: string;
      refreshToken?: string;
      expiresIn?: string;
      customToken?: string;
    } = {
      idToken: authResult.idToken,
      customToken: token,
    };

    if (authResult.refreshToken) {
      tokensToStore.refreshToken = authResult.refreshToken;
    }

    if (authResult.expiresIn) {
      tokensToStore.expiresIn = authResult.expiresIn;
    }

    internalStorage.storeAuthTokens(tokensToStore);

    // Notify callback if provided
    if (this.config.onTokenRefresh) {
      this.config.onTokenRefresh(authResult.idToken);
    }

    return authResult;
  }

  /**
   * Get current valid Firebase ID token with automatic detection and refresh
   */
  public async getIdToken(): Promise<string | null> {
    // If we have a getAuthToken function from the config, use it
    if (this.config.getAuthToken) {
      try {
        return await this.config.getAuthToken();
      } catch (error) {
        console.warn("Failed to get auth token from config function:", error);
      }
    }

    // Check if current token is still valid
    if (this.currentToken && this.tokenExpiry && Date.now() < this.tokenExpiry) {
      return this.currentToken;
    }

    // Try to get tokens from storage (cookies/localStorage)
    const storedTokens = internalStorage.getAuthTokens(this.cookieHeader);

    // If we have a valid stored token, use it
    if (storedTokens.idToken && !storedTokens.isExpired) {
      this.currentToken = storedTokens.idToken;
      return storedTokens.idToken;
    }

    // If stored token is expired but we have a refresh token, try to refresh
    if (storedTokens.refreshToken && storedTokens.isExpired) {
      try {
        const refreshed = await this.refreshIdToken(storedTokens.refreshToken);
        return refreshed.idToken;
      } catch (error) {
        console.warn("Failed to refresh stored token:", error);
      }
    }

    // Try to exchange custom token (from config or storage)
    const customToken = this.config.customToken || storedTokens.customToken;
    if (customToken) {
      try {
        const authResult = await this.exchangeCustomToken(customToken);
        return authResult.idToken;
      } catch (error) {
        console.error("Failed to exchange custom token:", error);
        return null;
      }
    }

    return null;
  }

  /**
   * Refresh Firebase ID token using refresh token
   */
  public async refreshIdToken(refreshToken: string): Promise<LCAuthToken> {
    const response = await ofetch<FirebaseRefreshResponse>("https://securetoken.googleapis.com/v1/token", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      }),
      params: {
        key: this.config.apiKey,
      },
    });

    const authResult: LCAuthToken = {
      idToken: response.id_token,
      refreshToken: response.refresh_token,
      expiresIn: response.expires_in,
      localId: response.user_id,
    };

    // Update current token
    this.currentToken = authResult.idToken;
    this.tokenExpiry = Date.now() + (parseInt(authResult.expiresIn || "3600") * 1000);

    // Store refreshed tokens in internal storage
    const tokensToStore: {
      idToken: string;
      refreshToken?: string;
      expiresIn?: string;
    } = {
      idToken: authResult.idToken,
    };

    if (authResult.refreshToken) {
      tokensToStore.refreshToken = authResult.refreshToken;
    }

    if (authResult.expiresIn) {
      tokensToStore.expiresIn = authResult.expiresIn;
    }

    internalStorage.storeAuthTokens(tokensToStore);

    // Notify callback if provided
    if (this.config.onTokenRefresh) {
      this.config.onTokenRefresh(authResult.idToken);
    }

    return authResult;
  }

  /**
   * Set up automatic token refresh
   */
  private setupTokenRefresh(refreshToken?: string): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    if (!refreshToken || !this.tokenExpiry) {
      return;
    }

    // Refresh token 5 minutes before expiry
    const refreshTime = this.tokenExpiry - Date.now() - (5 * 60 * 1000);
    
    if (refreshTime > 0) {
      this.refreshTimer = setTimeout(async () => {
        try {
          await this.refreshIdToken(refreshToken);
        } catch (error) {
          console.error("Auto token refresh failed:", error);
        }
      }, refreshTime);
    }
  }

  /**
   * Clear authentication state and stored tokens
   */
  public clearAuth(): void {
    this.currentToken = null;
    this.tokenExpiry = null;
    delete this.config.customToken;

    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }

    // Clear all stored tokens
    internalStorage.clearAllTokens();
  }

  /**
   * Check if user is authenticated
   */
  public async isAuthenticated(): Promise<boolean> {
    const token = await this.getIdToken();
    return token !== null;
  }

  /**
   * Get user information from Firebase
   */
  public async getUserInfo(): Promise<FirebaseUserInfo | null> {
    const idToken = await this.getIdToken();
    
    if (!idToken) {
      throw new Error("No authentication token available");
    }

    const response = await ofetch<FirebaseUserLookupResponse>(
      `https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${this.config.apiKey}`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: {
          idToken,
        },
      }
    );

    return response.users?.[0] || null;
  }
}

/**
 * Token manager for backward compatibility
 * @deprecated Use LCFirebaseAuth directly instead
 */
export class TokenManager {
  /**
   * Store custom token from LC backend
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static storeCustomToken(token: string): void {
    internalStorage.storeAuthTokens({ idToken: '', customToken: token });
  }

  /**
   * Get stored custom token
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static getCustomToken(): string | null {
    const tokens = internalStorage.getAuthTokens();
    return tokens.customToken;
  }

  /**
   * Store Firebase authentication tokens
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static storeFirebaseTokens(authResponse: LCAuthToken): void {
    const tokensToStore: {
      idToken: string;
      refreshToken?: string;
      expiresIn?: string;
    } = {
      idToken: authResponse.idToken,
    };

    if (authResponse.refreshToken) {
      tokensToStore.refreshToken = authResponse.refreshToken;
    }

    if (authResponse.expiresIn) {
      tokensToStore.expiresIn = authResponse.expiresIn;
    }

    internalStorage.storeAuthTokens(tokensToStore);
  }

  /**
   * Get stored Firebase ID token
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static getIdToken(): string | null {
    const tokens = internalStorage.getAuthTokens();
    return tokens.idToken;
  }

  /**
   * Get stored refresh token
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static getRefreshToken(): string | null {
    const tokens = internalStorage.getAuthTokens();
    return tokens.refreshToken;
  }

  /**
   * Check if Firebase ID token is expired
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static isTokenExpired(): boolean {
    const tokens = internalStorage.getAuthTokens();
    return tokens.isExpired;
  }

  /**
   * Clear all stored tokens
   * @deprecated This method is deprecated, tokens are now managed automatically
   */
  public static clearTokens(): void {
    internalStorage.clearAllTokens();
  }

  /**
   * Get valid token with auto-refresh
   * @deprecated Use auth.getIdToken() directly instead
   */
  public static async getValidToken(auth: LCFirebaseAuth): Promise<string | null> {
    return auth.getIdToken();
  }
}
