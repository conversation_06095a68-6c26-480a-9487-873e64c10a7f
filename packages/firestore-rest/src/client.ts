import { ofetch } from "ofetch";
import type {
  LCAuthConfig,
  FirestoreDocument,
  FirestoreListResponse,
  CreateDocumentRequest,
  UpdateDocumentRequest,
  QueryOptions,
  FirestoreStructuredQuery,
  FirestoreQueryResponse
} from "./types";
import {
  validateCollectionName,
  validateDocumentId,
  handleFirestoreError,
  toFirestoreValue
} from "./utils";
import { LCFirebaseAuth, type FirebaseUserInfo } from "./auth";

/**
 * Enhanced Firestore REST API Client with LC Backend Integration
 * Provides CRUD operations with automatic authentication handling
 * Supports both server-side and client-side environments with automatic token detection
 */
export class FirestoreClient {
  private config: LCAuthConfig;
  private baseURL: string;
  private client: typeof ofetch;
  private auth: LCFirebaseAuth;
  private cookieHeader: string | undefined;

  constructor(config: LCAuthConfig, cookieHeader?: string) {
    this.config = config;
    this.cookieHeader = cookieHeader;
    this.baseURL = `https://firestore.googleapis.com/v1/projects/${config.projectId}/databases/(default)/documents`;
    this.auth = new LCFirebaseAuth(config, cookieHeader);

    // Create ofetch client with dynamic headers
    this.client = ofetch.create({
      baseURL: this.baseURL,
      onRequest: async ({ options }) => {
        // Get headers with current auth token
        const headers = await this.getHeaders();
        options.headers = { ...options.headers, ...headers };
      },
      onResponseError: ({ response }) => {
        throw new Error(handleFirestoreError(response._data));
      }
    });
  }

  /**
   * Get headers with current authentication token
   */
  private async getHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
      "Accept": "application/json",
    };

    // Always include API key
    if (this.config.apiKey) {
      headers["X-Goog-Api-Key"] = this.config.apiKey;
    }

    // Try to get current auth token
    try {
      const authToken = await this.auth.getIdToken();
      if (authToken) {
        headers["Authorization"] = `Bearer ${authToken}`;
      }
    } catch (error) {
      console.warn("Failed to get auth token for request:", error);
    }

    return headers;
  }

  /**
   * Get the authentication manager
   */
  public getAuth(): LCFirebaseAuth {
    return this.auth;
  }

  /**
   * Set custom token from LC backend
   */
  public setCustomToken(token: string): void {
    this.auth.setCustomToken(token);
  }

  /**
   * Update authentication configuration
   */
  public updateAuthConfig(config: Partial<LCAuthConfig>): void {
    this.config = { ...this.config, ...config };
    this.auth = new LCFirebaseAuth(this.config);
  }

  /**
   * Create a new document
   */
  public async createDocument(
    collection: string, 
    data: Record<string, any>, 
    documentId?: string
  ): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    
    if (documentId) {
      validateDocumentId(documentId);
    }

    const url = documentId 
      ? `/${collection}?documentId=${documentId}`
      : `/${collection}`;

    const request: CreateDocumentRequest = {
      fields: {}
    };

    // Convert data to Firestore format
    for (const [key, value] of Object.entries(data)) {
      request.fields[key] = toFirestoreValue(value);
    }

    return await this.client(url, {
      method: "POST",
      body: request
    });
  }

  /**
   * Get a document by ID
   */
  public async getDocument(collection: string, documentId: string): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    return await this.client(`/${collection}/${documentId}`);
  }

  /**
   * Update a document
   */
  public async updateDocument(
    collection: string, 
    documentId: string, 
    data: Record<string, any>,
    merge: boolean = false
  ): Promise<FirestoreDocument> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    const request: UpdateDocumentRequest = {
      fields: {}
    };

    // Convert data to Firestore format
    for (const [key, value] of Object.entries(data)) {
      request.fields[key] = toFirestoreValue(value);
    }

    // Add update mask for partial updates
    if (!merge) {
      request.updateMask = {
        fieldPaths: Object.keys(data)
      };
    }

    const url = `/${collection}/${documentId}`;
    const queryParams = request.updateMask 
      ? `?updateMask.fieldPaths=${request.updateMask.fieldPaths.join("&updateMask.fieldPaths=")}`
      : "";

    return await this.client(`${url}${queryParams}`, {
      method: "PATCH",
      body: { fields: request.fields }
    });
  }

  /**
   * Delete a document
   */
  public async deleteDocument(collection: string, documentId: string): Promise<void> {
    validateCollectionName(collection);
    validateDocumentId(documentId);

    await this.client(`/${collection}/${documentId}`, {
      method: "DELETE"
    });
  }

  /**
   * List documents in a collection
   */
  public async listDocuments(
    collection: string, 
    pageSize?: number, 
    pageToken?: string
  ): Promise<FirestoreListResponse> {
    validateCollectionName(collection);

    const params = new URLSearchParams();
    if (pageSize) params.append("pageSize", pageSize.toString());
    if (pageToken) params.append("pageToken", pageToken);

    const url = `/${collection}${params.toString() ? `?${params.toString()}` : ""}`;
    
    return await this.client(url);
  }

  /**
   * Build structured query for complex queries
   */
  private buildStructuredQuery(collection: string, options: QueryOptions): FirestoreStructuredQuery {
    const query: FirestoreStructuredQuery = {
      from: [{ collectionId: collection }]
    };

    // Add where filters
    if (options.where && options.where.length > 0) {
      query.where = {
        compositeFilter: {
          op: "AND",
          filters: options.where.map(filter => ({
            fieldFilter: {
              field: { fieldPath: filter.field },
              op: filter.op,
              value: toFirestoreValue(filter.value)
            }
          }))
        }
      };
    }

    // Add order by
    if (options.orderBy && options.orderBy.length > 0) {
      query.orderBy = options.orderBy.map(order => ({
        field: { fieldPath: order.field },
        direction: order.direction
      }));
    }

    // Add limit
    if (options.limit) {
      query.limit = options.limit;
    }

    // Add offset
    if (options.offset) {
      query.offset = options.offset;
    }

    // Add startAt cursor
    if (options.startAt) {
      query.startAt = {
        values: options.startAt.map(toFirestoreValue),
        before: false
      };
    }

    // Add endAt cursor
    if (options.endAt) {
      query.endAt = {
        values: options.endAt.map(toFirestoreValue),
        before: true
      };
    }

    return query;
  }

  /**
   * Run a structured query
   */
  public async runQuery(collection: string, options: QueryOptions = {}): Promise<FirestoreDocument[]> {
    validateCollectionName(collection);

    const structuredQuery = this.buildStructuredQuery(collection, options);

    const response = await this.client<FirestoreQueryResponse[]>(":runQuery", {
      method: "POST",
      body: { structuredQuery }
    });

    // Extract documents from query response
    if (Array.isArray(response)) {
      return response
        .filter((item): item is FirestoreQueryResponse & { document: FirestoreDocument } =>
          item.document !== undefined
        )
        .map(item => item.document);
    }

    return [];
  }

  /**
   * Check authentication status
   */
  public async isAuthenticated(): Promise<boolean> {
    return await this.auth.isAuthenticated();
  }

  /**
   * Get current user information
   */
  public async getCurrentUser(): Promise<FirebaseUserInfo | null> {
    return await this.auth.getUserInfo();
  }

  /**
   * Sign out and clear authentication
   */
  public signOut(): void {
    this.auth.clearAuth();
  }
}
