/**
 * Integration utilities for LC Backend authentication with Firestore REST API
 */

import { createFirestore } from "./index";
import type { LCAuthConfig } from "./types";

/**
 * LC Backend integration configuration
 */
export type LCIntegrationConfig = {
  projectId: string;
  apiKey: string;
  lcBackendUrl?: string;
  getCustomToken?: () => Promise<string | null>;
  onAuthStateChange?: (authenticated: boolean) => void;
};

/**
 * Create Firestore instance integrated with LC Backend authentication
 * This function should be used in the dashboard to connect with existing auth flow
 */
export const createLCFirestore = (config: LCIntegrationConfig) => {
  const firestore = createFirestore({
    projectId: config.projectId,
    apiKey: config.apiKey,
    ...(config.getCustomToken !== undefined && { getAuthToken: config.getCustomToken }),
  });

  return {
    ...firestore,
    
    /**
     * Initialize with custom token from LC backend
     */
    initWithCustomToken: async (customToken: string) => {
      firestore.setCustomToken(customToken);
      
      // Exchange custom token for Firebase ID token
      try {
        const authResult = await firestore.auth.exchangeCustomToken(customToken);
        
        // Notify auth state change if callback provided
        if (config.onAuthStateChange) {
          config.onAuthStateChange(true);
        }
        
        return authResult;
      } catch (error) {
        console.error("Failed to exchange custom token:", error);
        
        if (config.onAuthStateChange) {
          config.onAuthStateChange(false);
        }
        
        throw error;
      }
    },

    /**
     * Sign out and clear all authentication state
     */
    signOut: () => {
      firestore.signOut();
      
      if (config.onAuthStateChange) {
        config.onAuthStateChange(false);
      }
    },

    /**
     * Check if user is authenticated with LC backend
     */
    isLCAuthenticated: async (): Promise<boolean> => {
      if (config.getCustomToken) {
        try {
          const token = await config.getCustomToken();
          return token !== null;
        } catch (error) {
          return false;
        }
      }
      return false;
    },
  };
};

/**
 * Helper function to create auth token getter from LC Core
 * This should be used in the dashboard to connect with existing auth state
 */
export const createLCAuthTokenGetter = (
  getLCAuthState: () => unknown, // Function to get current LC auth state
  getCustomTokenFromState?: (state: unknown) => string | null
) => {
  return async (): Promise<string | null> => {
    try {
      const authState = getLCAuthState();
      
      if (getCustomTokenFromState) {
        return getCustomTokenFromState(authState);
      }
      
      // Default extraction logic - adjust based on your auth state structure
      if (authState && typeof authState === 'object') {
        const state = authState as Record<string, unknown>;

        if (state.user && typeof state.user === 'object') {
          const user = state.user as Record<string, unknown>;
          if (typeof user.customToken === 'string') {
            return user.customToken;
          }
        }

        if (typeof state.token === 'string') {
          return state.token;
        }
      }
      
      return null;
    } catch (error) {
      console.error("Failed to get LC auth token:", error);
      return null;
    }
  };
};

/**
 * React hook for LC integrated Firestore
 * This should be used in React components that need Firestore with LC auth
 */
export const useLCFirestore = (config: LCIntegrationConfig) => {
  const firestore = createLCFirestore(config);
  
  return firestore;
};

/**
 * Default configuration for LC integration
 * These values should be overridden in the dashboard
 */
export const defaultLCConfig: Partial<LCIntegrationConfig> = {
  lcBackendUrl: "https://backend.leadconnectorhq.com",
};

export default createLCFirestore;
