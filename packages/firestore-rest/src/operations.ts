import { FirestoreClient } from "./client";
import type { 
  DocumentData, 
  QueryOptions, 
  Where<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  FirestoreDocument 
} from "./types";
import { 
  fromFirestoreFields, 
  extractDocumentId, 
  generateDocumentId 
} from "./utils";

/**
 * High-level Firestore operations wrapper
 * Provides a more user-friendly interface over the raw client
 */
export class FirestoreOperations {
  private client: FirestoreClient;

  constructor(client: FirestoreClient) {
    this.client = client;
  }

  /**
   * Add a document to a collection with auto-generated ID
   */
  public async add(collection: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const documentId = generateDocumentId();
    const doc = await this.client.createDocument(collection, data, documentId);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Set a document with a specific ID (create or overwrite)
   */
  public async set(collection: string, documentId: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const doc = await this.client.createDocument(collection, data, documentId);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Get a document by ID
   */
  public async get(collection: string, documentId: string): Promise<{ id: string; data: DocumentData } | null> {
    try {
      const doc = await this.client.getDocument(collection, documentId);
      
      return {
        id: extractDocumentId(doc.name),
        data: fromFirestoreFields(doc.fields)
      };
    } catch (error: unknown) {
      // Return null if document doesn't exist
      const errorMessage = error instanceof Error ? error.message : "";
      if (errorMessage.includes("NOT_FOUND") || errorMessage.includes("404")) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Update a document (merge with existing data)
   */
  public async update(collection: string, documentId: string, data: DocumentData): Promise<{ id: string; data: DocumentData }> {
    const doc = await this.client.updateDocument(collection, documentId, data, false);
    
    return {
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    };
  }

  /**
   * Delete a document
   */
  public async delete(collection: string, documentId: string): Promise<void> {
    await this.client.deleteDocument(collection, documentId);
  }

  /**
   * Get all documents in a collection
   */
  public async getAll(collection: string): Promise<Array<{ id: string; data: DocumentData }>> {
    const response = await this.client.listDocuments(collection);
    
    if (!response.documents) {
      return [];
    }

    return response.documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with filters
   */
  public async where(
    collection: string, 
    field: string, 
    operator: WhereFilter["op"], 
    value: any
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = {
      where: [{ field, op: operator, value }]
    };

    const documents = await this.client.runQuery(collection, options);
    
    return documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with multiple filters
   */
  public async whereMultiple(
    collection: string, 
    filters: WhereFilter[]
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = { where: filters };
    const documents = await this.client.runQuery(collection, options);
    
    return documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with ordering
   */
  public async orderBy(
    collection: string, 
    field: string, 
    direction: "asc" | "desc" = "asc"
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = {
      orderBy: [{ field, direction: direction === "asc" ? "ASCENDING" : "DESCENDING" }]
    };

    const documents = await this.client.runQuery(collection, options);
    
    return documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Query documents with limit
   */
  public async limit(collection: string, count: number): Promise<Array<{ id: string; data: DocumentData }>> {
    const options: QueryOptions = { limit: count };
    const documents = await this.client.runQuery(collection, options);
    
    return documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Complex query with multiple options
   */
  public async query(collection: string, options: QueryOptions): Promise<Array<{ id: string; data: DocumentData }>> {
    const documents = await this.client.runQuery(collection, options);
    
    return documents.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    }));
  }

  /**
   * Search documents by text (simple text matching)
   */
  public async search(
    collection: string, 
    field: string, 
    searchTerm: string
  ): Promise<Array<{ id: string; data: DocumentData }>> {
    // Get all documents and filter client-side for text search
    // Note: Firestore doesn't have built-in full-text search
    const allDocs = await this.getAll(collection);
    
    return allDocs.filter(doc => {
      const fieldValue = doc.data[field];
      if (typeof fieldValue === "string") {
        return fieldValue.toLowerCase().includes(searchTerm.toLowerCase());
      }
      return false;
    });
  }

  /**
   * Paginated query
   */
  public async paginate(
    collection: string, 
    pageSize: number, 
    pageToken?: string
  ): Promise<{
    documents: Array<{ id: string; data: DocumentData }>;
    nextPageToken?: string;
    hasMore: boolean;
  }> {
    const response = await this.client.listDocuments(collection, pageSize, pageToken);
    
    const documents = response.documents?.map(doc => ({
      id: extractDocumentId(doc.name),
      data: fromFirestoreFields(doc.fields)
    })) || [];

    const result: {
      documents: Array<{ id: string; data: DocumentData }>;
      nextPageToken?: string;
      hasMore: boolean;
    } = {
      documents,
      hasMore: !!response.nextPageToken
    };

    if (response.nextPageToken) {
      result.nextPageToken = response.nextPageToken;
    }

    return result;
  }

  /**
   * Count documents in a collection (approximate)
   */
  public async count(collection: string, filters?: WhereFilter[]): Promise<number> {
    const options: QueryOptions = filters ? { where: filters } : {};
    const documents = await this.client.runQuery(collection, options);
    return documents.length;
  }

  /**
   * Check if a document exists
   */
  public async exists(collection: string, documentId: string): Promise<boolean> {
    const doc = await this.get(collection, documentId);
    return doc !== null;
  }

  /**
   * Batch operations (simulate transaction-like behavior)
   */
  public async batch(operations: Array<{
    type: "create" | "update" | "delete";
    collection: string;
    documentId?: string;
    data?: DocumentData;
  }>): Promise<Array<{ id: string; data?: DocumentData }>> {
    const results = [];

    for (const operation of operations) {
      switch (operation.type) {
        case "create":
          if (operation.data) {
            const result = operation.documentId 
              ? await this.set(operation.collection, operation.documentId, operation.data)
              : await this.add(operation.collection, operation.data);
            results.push(result);
          }
          break;
        case "update":
          if (operation.documentId && operation.data) {
            const result = await this.update(operation.collection, operation.documentId, operation.data);
            results.push(result);
          }
          break;
        case "delete":
          if (operation.documentId) {
            await this.delete(operation.collection, operation.documentId);
            results.push({ id: operation.documentId });
          }
          break;
      }
    }

    return results;
  }

  /**
   * Get the underlying client for advanced operations
   */
  public getClient(): FirestoreClient {
    return this.client;
  }
}
