"use client";

/**
 * Client-side React hooks for Firestore operations
 * This file is specifically for client-side usage and includes the "use client" directive
 * Use this when you need to explicitly mark components as client-side in Next.js
 */

// Re-export all hooks from the main hooks file
export {
  useDocument,
  useCollection,
  useQuery,
  usePagination,
  useRealtimeDocument,
  useSearch,
  useFirestoreCRUD,
  useFirestoreAuth,
} from "./hooks";

// Re-export types that are commonly used with hooks
export type {
  UseFirestoreResult,
  UseFirestoreListResult,
  DocumentData,
  QueryOptions,
  WhereFilter,
} from "./types";
