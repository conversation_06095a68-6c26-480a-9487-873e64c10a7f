# @gd/firestore-rest

A comprehensive TypeScript library for interacting with Google Firestore using REST API calls, specifically designed to integrate with LC Backend authentication flow. This package provides all essential CRUD operations, querying capabilities, and React hooks without requiring the official Firebase SDK.

## Features

- ✅ **Environment-Agnostic** - Works in browser, server, edge, and worker environments
- ✅ **Web Standard APIs Only** - No Node.js dependencies, uses only Web APIs
- ✅ **Complete CRUD Operations** - Create, Read, Update, Delete documents
- ✅ **Advanced Querying** - Where clauses, ordering, pagination, limits
- ✅ **Search Functionality** - Text-based search across document fields
- ✅ **LC Backend Integration** - Seamless integration with existing authentication flow
- ✅ **Cookie-Based Token Sharing** - Automatic token synchronization between server/client
- ✅ **Next.js SSR/CSR Compatible** - Works identically in both environments
- ✅ **Simplified API** - Single `createFirestore()` call works everywhere
- ✅ **Zero Configuration** - Automatic environment detection and token management
- ✅ **React Hooks** - Ready-to-use hooks for React applications
- ✅ **TypeScript Support** - Full type safety and IntelliSense
- ✅ **Error Handling** - Comprehensive error handling and validation
- ✅ **Batch Operations** - Multiple operations in a single call
- ✅ **Real-time-like Updates** - Polling-based real-time updates
- ✅ **Data Type Support** - All Firestore data types (strings, numbers, arrays, objects, dates)

## Installation

This package is part of the monorepo workspace. It's automatically available as `@gd/firestore-rest`.

## Quick Start

### Basic Usage (Works in Both Server & Client)

```typescript
import { createFirestore } from "@gd/firestore-rest";

// Initialize Firestore - same code works everywhere!
const firestore = createFirestore({
  projectId: process.env.FIREBASE_PROJECT_ID!, // or NEXT_PUBLIC_FIREBASE_PROJECT_ID
  apiKey: process.env.FIREBASE_API_KEY!, // or NEXT_PUBLIC_FIREBASE_API_KEY
});

// Basic CRUD operations
const users = firestore.collection("users");

// Create a user
const newUser = await users.add({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
});

// Get a user
const user = await users.get(newUser.id);

// Update a user
await users.update(newUser.id, { age: 31 });

// Delete a user
await users.delete(newUser.id);
```

### Simple Token-Based Authentication

For simple use cases where you have a Firebase authentication token, you can use the simplified API:

```typescript
import { createFirestore } from "@gd/firestore-rest";

// Simple authentication with just projectId, apiKey, and token
const firestore = createFirestore({
  projectId: 'my-project-id',
  apiKey: 'my-api-key',
  token: 'my-auth-token' // Your Firebase ID token
});

// All the same CRUD operations work
const users = firestore.collection("users");
const newUser = await users.add({
  name: "John Doe",
  email: "<EMAIL>",
  age: 30,
});
```

This approach is perfect when you:
- Have a Firebase ID token from your authentication system
- Want minimal setup without complex token management
- Don't need automatic token refresh or cookie synchronization

### Cookie-Based Authentication (Automatic Server/Client Sync)

```typescript
import { createFirestore } from "@gd/firestore-rest";

// Client-side: User logs in and token is automatically stored in cookies
const handleLogin = async (customToken: string) => {
  const firestore = createFirestore({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  });

  // Set token - automatically stored in cookies for server access
  firestore.setCustomToken(customToken);
};

// Server-side: Automatically reads token from cookies
export async function getServerSideProps() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
    // Cookie header automatically detected in Next.js
  });

  // Uses token from cookies set by client
  const users = await firestore.collection("users").getAll();

  return { props: { users } };
}
```

### Next.js SSR/CSR Usage

This package is fully compatible with Next.js Server-Side Rendering (SSR) and Client-Side Rendering (CSR):

#### Server-Side Usage (SSR)

```typescript
// pages/users.tsx or app/users/page.tsx
import { createFirestore } from "@gd/firestore-rest";

export async function getServerSideProps() {
  const firestore = createFirestore({
    projectId: process.env.FIREBASE_PROJECT_ID!,
    apiKey: process.env.FIREBASE_API_KEY!,
  });

  // Fetch data on the server
  const users = await firestore.collection("users").getAll();

  return {
    props: {
      users,
    },
  };
}
```

#### Client-Side Usage (CSR)

```tsx
// For explicit client-side components, use the client-hooks export
"use client";

import { useCollection, useFirestoreCRUD } from "@gd/firestore-rest/client-hooks";

const UsersComponent = ({ firestore }) => {
  const { data: users, loading, error } = useCollection(
    firestore.operations,
    "users"
  );

  const { create, update, remove } = useFirestoreCRUD(
    firestore.operations,
    "users"
  );

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      {users.map(user => (
        <div key={user.id}>
          <h3>{user.name}</h3>
          <p>{user.email}</p>
        </div>
      ))}
    </div>
  );
};
```

#### Universal Usage (SSR + CSR)

```tsx
// This works in both server and client environments
import { useCollection, createFirestore } from "@gd/firestore-rest";

const UsersPage = ({ initialUsers }) => {
  const firestore = createFirestore({
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY!,
  });

  const { data: users, loading, error } = useCollection(
    firestore.operations,
    "users",
    { initialData: initialUsers }
  );

  return (
    <div>
      {users?.map(user => (
        <div key={user.id}>
          <h3>{user.name}</h3>
          <p>{user.email}</p>
        </div>
      ))}
    </div>
  );
};
```

## API Reference

### Core Classes

#### `FirestoreClient`
Low-level REST API client with automatic authentication handling.

#### `FirestoreOperations`
High-level wrapper providing user-friendly methods.

#### `LCFirebaseAuth`
Authentication manager for LC backend integration.

### Collection Methods

```typescript
const collection = firestore.collection("collectionName");

// CRUD Operations
await collection.add(data);                    // Create with auto-generated ID
await collection.set(id, data);               // Create/overwrite with specific ID
await collection.get(id);                     // Read document
await collection.update(id, data);            // Update document
await collection.delete(id);                  // Delete document

// Querying
await collection.getAll();                    // Get all documents
await collection.where(field, op, value);    // Filter documents
await collection.orderBy(field, direction);  // Order documents
await collection.limit(count);               // Limit results
await collection.query(options);             // Complex queries

// Utilities
await collection.search(field, term);        // Text search
await collection.count(filters);             // Count documents
await collection.exists(id);                 // Check existence
await collection.paginate(pageSize, token);  // Pagination
```

### Authentication Methods

```typescript
// Set custom token from LC backend
firestore.setCustomToken(token);

// Check authentication status
const isAuth = await firestore.isAuthenticated();

// Get current user info
const user = await firestore.getCurrentUser();

// Sign out
firestore.signOut();
```

### React Hooks

```typescript
// Single document
const { data, loading, error, refetch } = useDocument(
  firestore.operations, 
  "users", 
  userId
);

// Collection
const { data, loading, error, refetch } = useCollection(
  firestore.operations, 
  "users"
);

// Query with filters
const { data, loading, error } = useQuery(
  firestore.operations, 
  "users", 
  queryOptions
);

// Authentication status
const { isAuthenticated, user, loading, signOut } = useFirestoreAuth(
  firestore.operations
);

// CRUD operations
const { create, update, remove, loading, error } = useFirestoreCRUD(
  firestore.operations, 
  "users"
);
```

## Integration with LC Core

This package is designed to work seamlessly with the existing LC Core authentication system:

1. **Custom Tokens**: Uses custom tokens received from LC backend
2. **Auth State Integration**: Connects with existing auth state management
3. **Automatic Token Refresh**: Handles token refresh automatically
4. **Error Handling**: Graceful handling of authentication errors

## Data Types

Supports all Firestore data types:

```typescript
const documentData = {
  // Primitives
  name: "John Doe",           // string
  age: 30,                    // number (integer)
  height: 5.9,               // number (double)
  isActive: true,            // boolean
  
  // Complex types
  createdAt: new Date(),     // timestamp
  tags: ["tag1", "tag2"],    // array
  metadata: {                // map/object
    views: 100,
    likes: 50
  },
  
  // Null
  deletedAt: null
};
```

## Error Handling

```typescript
try {
  const user = await users.get("user-id");
} catch (error) {
  console.error("Firestore error:", error.message);
}

// Using hooks
const { data, error } = useDocument(firestore.operations, "users", "user-id");
if (error) {
  console.error("Hook error:", error);
}
```

## Best Practices

1. **Environment Variables**: Use environment variables for configuration
2. **Error Handling**: Implement proper error handling in components
3. **Loading States**: Show loading indicators during operations
4. **Authentication**: Ensure custom tokens are set before protected operations
5. **Validation**: Validate data before sending to Firestore
6. **Pagination**: Use pagination for large datasets
7. **Debouncing**: Use debounced search to avoid excessive API calls

## Limitations

- **No Real-time Updates**: Uses polling instead of real-time listeners
- **No Offline Support**: Requires internet connection
- **Rate Limits**: Subject to Firestore REST API rate limits
- **Complex Queries**: Some advanced Firestore features may not be available

## Security

- Always validate data on the server side
- Use Firestore security rules to protect your data
- Keep your API keys secure and use environment variables
- Implement proper authentication before accessing protected data

This package provides a complete, production-ready alternative to the Firebase Web SDK while maintaining excellent developer experience and seamless integration with your existing LC backend authentication system.
