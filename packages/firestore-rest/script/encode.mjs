import { readFileSync, writeFileSync, readdirSync } from "node:fs";
import { join } from "node:path";
import JavaScriptObfuscator from "javascript-obfuscator";

const distPath = "./dist";

function obfuscateFile(filePath) {
  const code = readFileSync(filePath, "utf8");
  const obfuscatedCode = JavaScriptObfuscator.obfuscate(code, {
    compact: true,
    stringArray: true,
    stringArrayEncoding: ["base64"],
  }).getObfuscatedCode();

  writeFileSync(filePath, obfuscatedCode);
}

function processDirectory(directory) {
  const files = readdirSync(directory, { withFileTypes: true });

  for (const file of files) {
    const fullPath = join(directory, file.name);

    if (file.isDirectory()) {
      processDirectory(fullPath);
    } else if (file.name.endsWith(".js")) {
      console.log(`Obfuscating: ${fullPath}`);
      obfuscateFile(fullPath);
    }
  }
}

// Start obfuscation process
console.log("Starting JavaScript obfuscation...");
processDirectory(distPath);
console.log("Obfuscation complete!");
